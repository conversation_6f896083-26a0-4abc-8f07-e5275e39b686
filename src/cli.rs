use clap::Parser;

#[derive(Parser, Debug)]
#[command(author, version, about, long_about = None)]
pub struct Cli {
    #[arg(long, value_name = "PORT")]
    pub proxy_localhost: Option<u16>,

    #[arg(long, value_name = "SESSION_ID")]
    pub restore: Option<String>,

    #[arg(long, value_name = "TOKEN_LIMIT", help = "Inject workspace files into system prompt with token limit (default: 1000)")]
    pub inject_workspace: Option<Option<usize>>,
}

pub fn parse_cli_args() -> Cli {
    Cli::parse()
}
