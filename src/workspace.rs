use std::fs;
use std::path::{Path, PathBuf};
use tiktoken_rs::cl100k_base;

/// Configuration for workspace scanning
#[derive(Debug, <PERSON>lone)]
pub struct WorkspaceScanConfig {
    pub token_limit: usize,
    pub max_depth: usize,
    pub include_hidden: bool,
}

impl Default for WorkspaceScanConfig {
    fn default() -> Self {
        Self {
            token_limit: 1000,
            max_depth: 3,
            include_hidden: false,
        }
    }
}

/// Represents a file or directory in the workspace
#[derive(Debug, <PERSON><PERSON>)]
pub struct WorkspaceItem {
    pub path: PathBuf,
    pub is_dir: bool,
    pub size: Option<u64>,
}

/// Scans the workspace directory and returns a structured representation
/// that fits within the specified token limit
pub fn scan_workspace(
    workspace_path: &Path,
    config: &WorkspaceScanConfig,
) -> Result<String, Box<dyn std::error::Error>> {
    let tokenizer = cl100k_base()?;
    
    // Collect all items first
    let mut items = Vec::new();
    collect_workspace_items(workspace_path, workspace_path, &mut items, 0, config)?;
    
    // Sort items by priority (directories first, then by name)
    items.sort_by(|a, b| {
        match (a.is_dir, b.is_dir) {
            (true, false) => std::cmp::Ordering::Less,
            (false, true) => std::cmp::Ordering::Greater,
            _ => a.path.cmp(&b.path),
        }
    });
    
    // Build the workspace structure string within token limit
    let mut workspace_str = String::new();
    workspace_str.push_str("Here is the structure of the current workspace:\n<workspace>\n");
    
    let header_tokens = tokenizer.encode_with_special_tokens(&workspace_str).len();
    let footer_tokens = tokenizer.encode_with_special_tokens("</workspace>\n").len();
    let available_tokens = config.token_limit.saturating_sub(header_tokens + footer_tokens);
    
    let mut current_tokens = 0;
    
    for item in items {
        let relative_path = item.path.strip_prefix(workspace_path)
            .unwrap_or(&item.path);
        
        let line = if item.is_dir {
            format!("{}/\n", relative_path.display())
        } else {
            format!("{}\n", relative_path.display())
        };
        
        let line_tokens = tokenizer.encode_with_special_tokens(&line).len();
        
        if current_tokens + line_tokens > available_tokens {
            workspace_str.push_str("...\n");
            break;
        }
        
        workspace_str.push_str(&line);
        current_tokens += line_tokens;
    }
    
    workspace_str.push_str("</workspace>\n");
    
    Ok(workspace_str)
}

/// Recursively collects workspace items
fn collect_workspace_items(
    current_path: &Path,
    workspace_root: &Path,
    items: &mut Vec<WorkspaceItem>,
    depth: usize,
    config: &WorkspaceScanConfig,
) -> Result<(), Box<dyn std::error::Error>> {
    if depth > config.max_depth {
        return Ok(());
    }
    
    let entries = fs::read_dir(current_path)?;
    
    for entry in entries {
        let entry = entry?;
        let path = entry.path();
        let file_name = path.file_name()
            .and_then(|n| n.to_str())
            .unwrap_or("");
        
        // Skip hidden files/directories unless configured to include them
        if !config.include_hidden && file_name.starts_with('.') {
            continue;
        }
        
        // Skip common build/cache directories
        if is_ignored_directory(file_name) {
            continue;
        }
        
        let metadata = entry.metadata()?;
        let is_dir = metadata.is_dir();
        let size = if is_dir { None } else { Some(metadata.len()) };
        
        items.push(WorkspaceItem {
            path: path.clone(),
            is_dir,
            size,
        });
        
        // Recursively scan subdirectories
        if is_dir {
            collect_workspace_items(&path, workspace_root, items, depth + 1, config)?;
        }
    }
    
    Ok(())
}

/// Checks if a directory should be ignored during scanning
fn is_ignored_directory(name: &str) -> bool {
    matches!(name, 
        "target" | "node_modules" | ".git" | ".svn" | ".hg" | 
        "build" | "dist" | "out" | ".next" | ".nuxt" |
        "__pycache__" | ".pytest_cache" | ".mypy_cache" |
        ".gradle" | ".idea" | ".vscode" | ".vs" |
        "coverage" | ".nyc_output" | "logs" | "tmp" | "temp"
    )
}

/// Creates a workspace injection string for the system prompt
pub fn create_workspace_injection(
    workspace_path: &Path,
    token_limit: usize,
) -> Result<String, Box<dyn std::error::Error>> {
    let config = WorkspaceScanConfig {
        token_limit,
        ..Default::default()
    };
    
    scan_workspace(workspace_path, &config)
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::fs;
    use tempfile::TempDir;
    
    #[test]
    fn test_workspace_scanning() {
        let temp_dir = TempDir::new().unwrap();
        let workspace_path = temp_dir.path();
        
        // Create test structure
        fs::create_dir(workspace_path.join("src")).unwrap();
        fs::write(workspace_path.join("src/main.rs"), "fn main() {}").unwrap();
        fs::write(workspace_path.join("Cargo.toml"), "[package]\nname = \"test\"").unwrap();
        fs::write(workspace_path.join("README.md"), "# Test Project").unwrap();
        
        let config = WorkspaceScanConfig::default();
        let result = scan_workspace(workspace_path, &config).unwrap();
        
        assert!(result.contains("<workspace>"));
        assert!(result.contains("</workspace>"));
        assert!(result.contains("src/"));
        assert!(result.contains("Cargo.toml"));
        assert!(result.contains("README.md"));
    }
}
