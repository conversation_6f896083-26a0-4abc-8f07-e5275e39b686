use crate::app::App;
use crate::cli_prompt::{CurrentPromptBufferState, TokenCache};
use crate::llm_client;
use nu_ansi_term::{Color, Style};
use reedline::{Hinter, History, SearchQuery};
use std::sync::{Arc, Mutex};
use tiktoken_rs::{cl100k_base, CoreBPE};

/// A hinter that combines history-based hints with real-time token counting
/// and updates the prompt buffer state on every character input
pub struct TokenCountingHinter {
    // History hinting functionality (similar to DefaultHinter)
    style: Style,
    current_hint: String,
    min_chars: usize,

    // Token counting functionality
    app_arc: Arc<Mutex<App>>,
    prompt_buffer_state: Arc<Mutex<CurrentPromptBufferState>>,
    tokenizer: Arc<CoreBPE>,
    token_cache: Arc<Mutex<TokenCache>>,
}

impl TokenCountingHinter {
    pub fn new(
        app_arc: Arc<Mutex<App>>,
        prompt_buffer_state: Arc<Mutex<CurrentPromptBufferState>>,
    ) -> Self {
        let tokenizer = Arc::new(cl100k_base().unwrap_or_else(|e| {
            eprintln!(
                "Failed to load cl100k_base tokenizer: {}. Token counting will be inaccurate.",
                e
            );
            panic!("Tokenizer cl100k_base failed to load: {}", e);
        }));

        // Note: System prompt tokens are now calculated dynamically since workspace injection can change them
        let token_cache = Arc::new(Mutex::new(TokenCache {
            system_prompt_tokens: 0, // Will be calculated dynamically
            history_tokens: 0,
            history_version: 0,
        }));

        Self {
            style: Style::new().fg(Color::LightGray),
            current_hint: String::new(),
            min_chars: 1,
            app_arc,
            prompt_buffer_state,
            tokenizer,
            token_cache,
        }
    }

    /// A builder that sets the style applied to the hint as part of the buffer
    #[must_use]
    pub fn with_style(mut self, style: Style) -> Self {
        self.style = style;
        self
    }

    /// Update the prompt buffer state with the current line content
    fn update_buffer_content(&self, content: &str) {
        if let Ok(mut buffer_state) = self.prompt_buffer_state.lock() {
            buffer_state.current_buffer_content = content.to_string();
        }
    }

    /// Calculate the total token count for the current buffer content
    fn get_token_count_for_buffer(&self, buffer_content: &str) -> usize {
        let app_guard = self.app_arc.lock().unwrap();
        let mut cache_guard = self.token_cache.lock().unwrap();

        let current_history_version = app_guard.conversation_history_for_llm.len();

        // Update history tokens cache if conversation history changed
        if cache_guard.history_version != current_history_version {
            cache_guard.history_tokens = app_guard
                .conversation_history_for_llm
                .iter()
                .map(|message| {
                    self.tokenizer
                        .encode_with_special_tokens(&message.content)
                        .len()
                })
                .sum();
            cache_guard.history_version = current_history_version;
        }

        let current_buffer_tokens = self
            .tokenizer
            .encode_with_special_tokens(buffer_content)
            .len();

        cache_guard.system_prompt_tokens + cache_guard.history_tokens + current_buffer_tokens
    }

    /// Get the first token from a string (similar to get_first_token from hinter module)
    fn get_first_token(input: &str) -> String {
        input
            .split_whitespace()
            .next()
            .unwrap_or_default()
            .to_string()
    }
}

impl Hinter for TokenCountingHinter {
    fn handle(
        &mut self,
        line: &str,
        _pos: usize,
        history: &dyn History,
        _use_ansi_coloring: bool,
    ) -> String {
        // Update the prompt buffer state with the current line content
        // This is the key functionality that makes token counting work in real-time
        self.update_buffer_content(line);

        // Generate history-based hint (similar to DefaultHinter)
        self.current_hint = if line.chars().count() >= self.min_chars {
            // Try to find a history entry that starts with the current line
            if let Ok(search_results) = history.search(SearchQuery::last_with_prefix(
                line.to_string(),
                history.session(),
            )) {
                search_results.first().map_or_else(String::new, |entry| {
                    entry
                        .command_line
                        .get(line.len()..)
                        .unwrap_or_default()
                        .to_string()
                })
            } else {
                String::new()
            }
        } else {
            String::new()
        };
        let token_count = self.get_token_count_for_buffer(line);
        // prefixing with a newline hides the hint from the prompt indicator on lots of buffer text apparently
        let token_display = format!("   [{} tokens]", token_count);
        self.style
            .paint(self.current_hint.clone() + &token_display)
            .to_string()
    }

    fn complete_hint(&self) -> String {
        self.current_hint.clone()
    }

    fn next_hint_token(&self) -> String {
        Self::get_first_token(&self.current_hint)
    }
}
