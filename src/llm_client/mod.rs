pub mod api;
pub mod tools;
pub mod types;
pub use api::call_llm_api;
pub use types::{ChatMessage, ToolCallAction};

use crate::cli::Cli;
use crate::workspace;
use std::path::Path;

pub const BASE_SYSTEM_PROMPT: &str = include_str!("../../prompts/system_prompt.txt");

/// Creates the system prompt, optionally including workspace structure
pub fn create_system_prompt(cli_args: &Cli, workspace_path: Option<&Path>) -> String {
    let mut system_prompt = BASE_SYSTEM_PROMPT.to_string();

    // Check if workspace injection is enabled
    if let Some(token_limit_opt) = &cli_args.inject_workspace {
        let token_limit = token_limit_opt.unwrap_or(1000);

        if let Some(workspace_path) = workspace_path {
            match workspace::create_workspace_injection(workspace_path, token_limit) {
                Ok(workspace_content) => {
                    system_prompt.push_str("\n\n");
                    system_prompt.push_str(&workspace_content);
                }
                Err(e) => {
                    eprintln!("Warning: Failed to inject workspace structure: {}", e);
                }
            }
        }
    }

    system_prompt
}

// Function to attempt parsing a string as a ToolCallAction
pub fn try_parse_tool_call(content: &str) -> Option<ToolCallAction> {
    let trimmed_content = content.trim();
    if trimmed_content.starts_with('{') && trimmed_content.ends_with('}') {
        serde_json::from_str::<ToolCallAction>(trimmed_content).ok()
    } else {
        None
    }
}
